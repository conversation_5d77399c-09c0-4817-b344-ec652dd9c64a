#!/usr/bin/env python3
"""
Net Zero Tender Finder
A Python application to find Net Zero, Climate, and Sustainability opportunities
from the UK Find a Tender service using their OCDS API.
"""

import requests
import json
import re
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import csv
import os
from dataclasses import dataclass
import time

@dataclass
class TenderOpportunity:
    """Data class to store tender opportunity information"""
    ocid: str
    notice_id: str
    title: str
    description: str
    status: str
    stage: str
    publication_date: str
    closing_date: Optional[str]
    submission_deadline: Optional[str]
    buyer_name: str
    buyer_contact_email: Optional[str]
    buyer_contact_phone: Optional[str]
    value_amount: Optional[float]
    value_currency: str
    contract_duration: Optional[str]
    url: str
    matched_keywords: List[str]
    cpv_codes: List[str]
    procedure_type: Optional[str]
    award_criteria: List[str]

class NetZeroTenderFinder:
    """Main class for finding Net Zero tender opportunities"""
    
    def __init__(self):
        self.base_url = "https://www.find-tender.service.gov.uk/api/1.0/ocdsReleasePackages"
        self.keywords = [
            "net zero", "net-zero", "carbon neutral", "carbon neutrality",
            "sustainability", "sustainable", "climate change", "climate",
            "renewable energy", "clean energy", "green energy",
            "emissions reduction", "carbon reduction", "decarbonisation",
            "environment", "environmental", "air quality", "air pollution",
            "energy efficiency", "solar", "wind energy", "electric vehicle",
            "ev charging", "heat pump", "insulation", "retrofit"
        ]
        self.stages = ["planning", "tender"]  # Pipeline not included as per your requirements
        self.session = requests.Session()
        self.session.headers.update({
            'Accept': 'application/json',
            'User-Agent': 'NetZeroTenderFinder/1.0'
        })
        
    def search_opportunities(self, days_back: int = 7, max_results: int = 500) -> List[TenderOpportunity]:
        """
        Search for Net Zero opportunities from the last N days
        
        Args:
            days_back: Number of days to look back for opportunities
            max_results: Maximum number of results to process
            
        Returns:
            List of matching tender opportunities
        """
        opportunities = []
        
        # Calculate date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)
        
        print(f"Searching for opportunities from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
        
        # Build API parameters
        params = {
            'stages': ','.join(self.stages),
            'updatedFrom': start_date.strftime('%Y-%m-%dT00:00:00'),
            'updatedTo': end_date.strftime('%Y-%m-%dT23:59:59'),
            'limit': 100
        }
        
        total_processed = 0
        cursor = None
        
        while total_processed < max_results:
            if cursor:
                params['cursor'] = cursor
                
            try:
                print(f"Fetching batch (processed: {total_processed})...")
                response = self.session.get(self.base_url, params=params)
                response.raise_for_status()
                
                data = response.json()
                
                if 'releases' not in data or not data['releases']:
                    print("No more results found")
                    break
                
                # Process each release
                batch_opportunities = []
                for release in data['releases']:
                    opportunity = self._process_release(release)
                    if opportunity:
                        batch_opportunities.append(opportunity)
                
                opportunities.extend(batch_opportunities)
                total_processed += len(data['releases'])
                
                print(f"Found {len(batch_opportunities)} matching opportunities in this batch")
                
                # Check for next page
                cursor = self._extract_next_cursor(response.headers)
                if not cursor:
                    print("No more pages available")
                    break
                    
                # Add small delay to be respectful to the API
                time.sleep(0.5)
                
            except requests.exceptions.RequestException as e:
                print(f"Error fetching data: {e}")
                break
                
        print(f"Total opportunities found: {len(opportunities)}")
        return opportunities
    
    def _process_release(self, release: Dict) -> Optional[TenderOpportunity]:
        """Process a single release and check if it matches our criteria"""
        
        try:
            # Extract basic information
            ocid = release.get('ocid', '')
            notice_id = release.get('id', '')
            publication_date = release.get('date', '')
            stage = ','.join(release.get('tag', []))
            
            # Extract tender information
            tender = release.get('tender', {})
            title = tender.get('title', '')
            description = tender.get('description', '')
            status = tender.get('status', '')
            
            # Extract closing/submission dates
            closing_date = None
            submission_deadline = None
            
            # Check tender period for closing dates
            tender_period = tender.get('tenderPeriod', {})
            if tender_period.get('endDate'):
                closing_date = tender_period['endDate']
                submission_deadline = tender_period['endDate']
            
            # Check enquiry period as alternative
            enquiry_period = tender.get('enquiryPeriod', {})
            if not closing_date and enquiry_period.get('endDate'):
                closing_date = enquiry_period['endDate']
            
            # Check award period
            award_period = tender.get('awardPeriod', {})
            if award_period.get('startDate') and not closing_date:
                closing_date = award_period['startDate']
            
            # Extract buyer information
            buyer_info = release.get('buyer', {})
            buyer_name = buyer_info.get('name', 'Unknown')
            
            # Find buyer contact details from parties
            buyer_contact_email = None
            buyer_contact_phone = None
            parties = release.get('parties', [])
            
            for party in parties:
                if 'buyer' in party.get('roles', []):
                    contact_point = party.get('contactPoint', {})
                    if contact_point.get('email'):
                        buyer_contact_email = contact_point['email']
                    if contact_point.get('telephone'):
                        buyer_contact_phone = contact_point['telephone']
                    break
            
            # Extract value information
            value_info = tender.get('value', {})
            value_amount = value_info.get('amount')
            value_currency = value_info.get('currency', 'GBP')
            
            # Extract contract duration
            contract_duration = None
            contract_period = tender.get('contractPeriod', {})
            if contract_period.get('durationInDays'):
                days = contract_period['durationInDays']
                contract_duration = f"{days} days"
            elif contract_period.get('startDate') and contract_period.get('endDate'):
                contract_duration = f"From {contract_period['startDate']} to {contract_period['endDate']}"
            
            # Extract procedure type
            procedure_type = tender.get('procurementMethod', '')
            if not procedure_type:
                procedure_type = tender.get('procurementMethodDetails', '')
            
            # Extract award criteria
            award_criteria = []
            award_criteria_info = tender.get('awardCriteria', {})
            if award_criteria_info.get('criteria'):
                for criterion in award_criteria_info['criteria']:
                    if criterion.get('type'):
                        award_criteria.append(criterion['type'])
            
            # Extract CPV codes
            cpv_codes = []
            classification = tender.get('classification', {})
            if classification.get('scheme') == 'CPV':
                cpv_codes.append(f"{classification.get('id', '')}: {classification.get('description', '')}")
            
            # Check lots for additional information
            lots = tender.get('lots', [])
            lot_descriptions = []
            for lot in lots:
                # CPV codes from lots
                lot_classification = lot.get('classification', {})
                if lot_classification.get('scheme') == 'CPV':
                    cpv_codes.append(f"{lot_classification.get('id', '')}: {lot_classification.get('description', '')}")
                
                # Lot descriptions
                lot_desc = lot.get('description', '')
                if lot_desc:
                    lot_descriptions.append(f"Lot {lot.get('id', '')}: {lot_desc}")
                
                # Check for lot-specific closing dates
                lot_tender_period = lot.get('tenderPeriod', {})
                if not closing_date and lot_tender_period.get('endDate'):
                    closing_date = lot_tender_period['endDate']
                
                # Award criteria from lots
                lot_award_criteria = lot.get('awardCriteria', {})
                if lot_award_criteria.get('criteria'):
                    for criterion in lot_award_criteria['criteria']:
                        if criterion.get('type') and criterion['type'] not in award_criteria:
                            award_criteria.append(criterion['type'])
            
            # Combine main description with lot descriptions (full text, no truncation)
            full_description = description
            if lot_descriptions:
                full_description += " | " + " | ".join(lot_descriptions)
            
            # Combine all text to search (use full description)
            search_text = f"{title} {full_description}".lower()
            
            # Check for keyword matches
            matched_keywords = []
            for keyword in self.keywords:
                if keyword.lower() in search_text:
                    matched_keywords.append(keyword)
            
            # Only return if we found matching keywords
            if matched_keywords:
                # Construct the URL to the full notice
                url = f"https://www.find-tender.service.gov.uk/Notice/{notice_id}"
                
                return TenderOpportunity(
                    ocid=ocid,
                    notice_id=notice_id,
                    title=title,
                    description=full_description,  # Full description, no truncation
                    status=status,
                    stage=stage,
                    publication_date=publication_date,
                    closing_date=closing_date,
                    submission_deadline=submission_deadline,
                    buyer_name=buyer_name,
                    buyer_contact_email=buyer_contact_email,
                    buyer_contact_phone=buyer_contact_phone,
                    value_amount=value_amount,
                    value_currency=value_currency,
                    contract_duration=contract_duration,
                    url=url,
                    matched_keywords=matched_keywords,
                    cpv_codes=cpv_codes,
                    procedure_type=procedure_type,
                    award_criteria=award_criteria
                )
                
        except Exception as e:
            print(f"Error processing release {release.get('id', 'unknown')}: {e}")
            
        return None
    
    def _extract_next_cursor(self, headers: Dict) -> Optional[str]:
        """Extract next page cursor from response headers"""
        # The API typically includes pagination info in headers or the response body
        # This is a placeholder - you may need to adjust based on actual API behavior
        return None
    
    def save_to_csv(self, opportunities: List[TenderOpportunity], filename: str = None):
        """Save opportunities to CSV file"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"net_zero_opportunities_{timestamp}.csv"
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'Notice ID', 'Title', 'Buyer', 'Buyer Contact Email', 'Buyer Contact Phone',
                'Status', 'Stage', 'Publication Date', 'Closing Date', 'Submission Deadline',
                'Value Amount', 'Value Currency', 'Contract Duration', 'Procedure Type',
                'Award Criteria', 'Matched Keywords', 'CPV Codes', 'Description', 'URL'
            ]
            
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for opp in opportunities:
                writer.writerow({
                    'Notice ID': opp.notice_id,
                    'Title': opp.title,
                    'Buyer': opp.buyer_name,
                    'Buyer Contact Email': opp.buyer_contact_email,
                    'Buyer Contact Phone': opp.buyer_contact_phone,
                    'Status': opp.status,
                    'Stage': opp.stage,
                    'Publication Date': opp.publication_date,
                    'Closing Date': opp.closing_date,
                    'Submission Deadline': opp.submission_deadline,
                    'Value Amount': opp.value_amount,
                    'Value Currency': opp.value_currency,
                    'Contract Duration': opp.contract_duration,
                    'Procedure Type': opp.procedure_type,
                    'Award Criteria': ', '.join(opp.award_criteria) if opp.award_criteria else '',
                    'Matched Keywords': ', '.join(opp.matched_keywords),
                    'CPV Codes': ' | '.join(opp.cpv_codes),
                    'Description': opp.description,
                    'URL': opp.url
                })
        
        print(f"Results saved to {filename}")
        return filename
    
    def print_summary(self, opportunities: List[TenderOpportunity]):
        """Print a summary of found opportunities"""
        if not opportunities:
            print("No opportunities found matching your criteria.")
            return
        
        print(f"\n=== SUMMARY ===")
        print(f"Total opportunities found: {len(opportunities)}")
        
        # Group by stage
        stage_counts = {}
        for opp in opportunities:
            stage_counts[opp.stage] = stage_counts.get(opp.stage, 0) + 1
        
        print("\nBy stage:")
        for stage, count in stage_counts.items():
            print(f"  {stage}: {count}")
        
        # Top keywords
        keyword_counts = {}
        for opp in opportunities:
            for keyword in opp.matched_keywords:
                keyword_counts[keyword] = keyword_counts.get(keyword, 0) + 1
        
        print("\nTop keywords found:")
        sorted_keywords = sorted(keyword_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        for keyword, count in sorted_keywords:
            print(f"  {keyword}: {count}")
        
        print(f"\n=== TOP 5 OPPORTUNITIES ===")
        for i, opp in enumerate(opportunities[:5], 1):
            print(f"\n{i}. {opp.title}")
            print(f"   Buyer: {opp.buyer_name}")
            print(f"   Status: {opp.status} | Stage: {opp.stage}")
            if opp.closing_date:
                print(f"   ⏰ Closing Date: {opp.closing_date}")
            if opp.submission_deadline:
                print(f"   📝 Submission Deadline: {opp.submission_deadline}")
            print(f"   Keywords: {', '.join(opp.matched_keywords)}")
            print(f"   URL: {opp.url}")
            if opp.value_amount:
                print(f"   Value: {opp.value_currency} {opp.value_amount:,.2f}")
            if opp.contract_duration:
                print(f"   Duration: {opp.contract_duration}")
            if opp.procedure_type:
                print(f"   Procedure: {opp.procedure_type}")

def main():
    """Main function to run the tender finder"""
    print("🌱 Net Zero Tender Finder - UK Find a Tender Service")
    print("=" * 60)
    
    finder = NetZeroTenderFinder()
    
    # Search for opportunities from the last 14 days
    opportunities = finder.search_opportunities(days_back=14, max_results=1000)
    
    if opportunities:
        # Print summary
        finder.print_summary(opportunities)
        
        # Save to CSV
        csv_filename = finder.save_to_csv(opportunities)
        print(f"\n📊 Full results saved to: {csv_filename}")
        
        # Ask if user wants to see all results
        try:
            show_all = input(f"\nShow all {len(opportunities)} opportunities? (y/n): ").lower().strip()
            if show_all == 'y':
                print(f"\n=== ALL OPPORTUNITIES ===")
                for i, opp in enumerate(opportunities, 1):
                    print(f"\n{i}. {opp.title}")
                    print(f"   Notice ID: {opp.notice_id}")
                    print(f"   Buyer: {opp.buyer_name}")
                    if opp.buyer_contact_email:
                        print(f"   Contact Email: {opp.buyer_contact_email}")
                    if opp.buyer_contact_phone:
                        print(f"   Contact Phone: {opp.buyer_contact_phone}")
                    print(f"   Status: {opp.status} | Stage: {opp.stage}")
                    print(f"   Publication: {opp.publication_date}")
                    if opp.closing_date:
                        print(f"   ⏰ Closing Date: {opp.closing_date}")
                    if opp.submission_deadline:
                        print(f"   📝 Submission Deadline: {opp.submission_deadline}")
                    print(f"   Keywords: {', '.join(opp.matched_keywords)}")
                    if opp.value_amount:
                        print(f"   Value: {opp.value_currency} {opp.value_amount:,.2f}")
                    if opp.contract_duration:
                        print(f"   Duration: {opp.contract_duration}")
                    if opp.procedure_type:
                        print(f"   Procedure: {opp.procedure_type}")
                    if opp.award_criteria:
                        print(f"   Award Criteria: {', '.join(opp.award_criteria)}")
                    print(f"   URL: {opp.url}")
                    print(f"   Description: {opp.description}")
                    if opp.cpv_codes:
                        print(f"   CPV Codes: {' | '.join(opp.cpv_codes)}")
        except KeyboardInterrupt:
            print("\n\nSearch completed!")
    else:
        print("No Net Zero opportunities found in the specified time period.")
        print("Try increasing the number of days to search back, or check if there are new opportunities.")

if __name__ == "__main__":
    main()
